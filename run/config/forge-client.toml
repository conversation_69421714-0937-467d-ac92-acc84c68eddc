
#Client only settings, mostly things related to rendering
[client]
	#Enable Forge to queue all chunk updates to the Chunk Update thread.
	#May increase FPS significantly, but may also cause weird rendering lag.
	#Not recommended for computers without a significant number of cores available.
	alwaysSetupTerrainOffThread = false
	#EXPERIMENTAL: Enable the Forge block rendering pipeline - fixes the lighting of custom models.
	experimentalForgeLightPipelineEnabled = false
	#When enabled, Forge will show any warnings that occurred during loading.
	showLoadWarnings = true
	#Set to true to use a combined DEPTH_STENCIL attachment instead of two separate ones.
	useCombinedDepthStencilAttachment = false
	#[DEPRECATED] Does nothing anymore, IPv6 addresses will be compressed always
	compressLanIPv6Addresses = true
	#During block model baking, manually calculates the normal for all faces.
	#This was the default behavior of forge between versions 31.0 and 47.1.
	#May result in differences between vanilla rendering and forge rendering.
	#Will only produce differences for blocks that contain non-axis aligned faces.
	#You will need to reload your resources to see results.
	calculateAllNormals = false
	#When enabled, a slightly biased Direction#getNearest calculation will be used to prevent normal fighting on 45 degree angle faces.
	stabilizeDirectionGetNearest = true
	#When enabled, Forge will allow mipmaps to be lowered in real-time. This is the default behavior in vanilla. Use this if you experience issues with resource packs that use textures lower than 8x8.
	allowMipmapLowering = false

