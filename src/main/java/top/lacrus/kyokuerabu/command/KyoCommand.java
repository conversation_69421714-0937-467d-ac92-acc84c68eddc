package top.lacrus.kyokuerabu.command;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.tree.LiteralCommandNode;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;

public class KyoCommand {
    
    public static void register(CommandDispatcher<CommandSourceStack> dispatcher) {
        LiteralCommandNode<CommandSourceStack> kyoCommand = dispatcher.register(
            Commands.literal("kyo")
                .requires(source -> source.hasPermission(0))
                .then(PlayOnlineCommand.register())
        );
    }
}
