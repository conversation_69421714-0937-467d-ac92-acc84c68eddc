package top.lacrus.kyokuerabu.command;

import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.builder.ArgumentBuilder;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.commands.arguments.EntityArgument;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerPlayer;
import net.minecraftforge.network.PacketDistributor;
import top.lacrus.kyokuerabu.network.NetworkHandler;
import top.lacrus.kyokuerabu.network.PlayMusicPacket;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.Collection;

public class PlayOnlineCommand {
    
    public static ArgumentBuilder<CommandSourceStack, ?> register() {
        return Commands.literal("play_online")
            .then(Commands.argument("targets", EntityArgument.players())
                .then(Commands.argument("url", StringArgumentType.greedyString())
                    .executes(PlayOnlineCommand::execute)
                )
            );
    }
    
    private static int execute(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        CommandSourceStack source = context.getSource();
        Collection<ServerPlayer> targets = EntityArgument.getPlayers(context, "targets");
        String musicUrl = StringArgumentType.getString(context, "url");
        
        // 验证URL格式
        if (!isValidUrl(musicUrl)) {
            source.sendFailure(Component.literal("§c无效的URL格式: " + musicUrl));
            return 0;
        }
        
        // 验证是否为支持的音频格式
        if (!isSupportedAudioFormat(musicUrl)) {
            source.sendFailure(Component.literal("§c支持的音频格式: .mp3, .wav, .aiff, .au"));
            return 0;
        }
        
        // 向目标玩家发送播放音乐的数据包
        PlayMusicPacket packet = new PlayMusicPacket(musicUrl);
        
        for (ServerPlayer player : targets) {
            NetworkHandler.INSTANCE.send(
                PacketDistributor.PLAYER.with(() -> player),
                packet
            );
        }
        
        // 向命令执行者发送成功消息
        if (targets.size() == 1) {
            ServerPlayer target = targets.iterator().next();
            source.sendSuccess(() -> Component.literal(
                "§a已向玩家 " + target.getName().getString() + " 发送音乐播放指令: " + musicUrl
            ), true);
        } else {
            source.sendSuccess(() -> Component.literal(
                "§a已向 " + targets.size() + " 个玩家发送音乐播放指令: " + musicUrl
            ), true);
        }
        
        return targets.size();
    }
    
    private static boolean isValidUrl(String urlString) {
        try {
            URL url = new URL(urlString);
            String protocol = url.getProtocol();
            return "http".equals(protocol) || "https".equals(protocol);
        } catch (MalformedURLException e) {
            return false;
        }
    }
}
