package top.lacrus.kyokuerabu.command;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.commands.arguments.EntityArgument;
import net.minecraft.network.chat.Component;
import net.minecraft.server.level.ServerPlayer;
import top.lacrus.kyokuerabu.audio.SimpleAudioPlayer;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.Collection;

public class SimplePlayCommand {
    
    public static void register(CommandDispatcher<CommandSourceStack> dispatcher) {
        dispatcher.register(
            Commands.literal("kyo")
                .requires(source -> source.hasPermission(2)) // OP权限
                .then(Commands.literal("play_online")
                    .then(Commands.argument("targets", EntityArgument.players())
                        .then(Commands.argument("url", StringArgumentType.greedyString())
                            .executes(SimplePlayCommand::execute)
                        )
                    )
                )
        );
    }
    
    private static int execute(CommandContext<CommandSourceStack> context) throws CommandSyntaxException {
        CommandSourceStack source = context.getSource();
        Collection<ServerPlayer> targets = EntityArgument.getPlayers(context, "targets");
        String musicUrl = StringArgumentType.getString(context, "url");
        
        // 验证URL格式
        if (!isValidUrl(musicUrl)) {
            source.sendFailure(Component.literal("§c无效的URL格式: " + musicUrl));
            return 0;
        }
        
        // 验证是否为支持的音频格式
        if (!isSupportedAudioFormat(musicUrl)) {
            source.sendFailure(Component.literal("§c支持的音频格式: .mp3, .wav, .aiff, .au"));
            return 0;
        }
        
        // 直接在服务端播放音乐（这会在服务器机器上播放）
        try {
            SimpleAudioPlayer.playMusic(musicUrl);
            
            // 向命令执行者发送成功消息
            if (targets.size() == 1) {
                ServerPlayer target = targets.iterator().next();
                source.sendSuccess(() -> Component.literal(
                    "§a开始播放音乐: " + musicUrl + " (在服务器端播放)"
                ), true);
            } else {
                source.sendSuccess(() -> Component.literal(
                    "§a开始播放音乐: " + musicUrl + " (在服务器端播放)"
                ), true);
            }
            
            // 向目标玩家发送通知
            for (ServerPlayer player : targets) {
                player.sendSystemMessage(Component.literal(
                    "§a[Kyokuerabu] 服务器正在播放音乐: " + musicUrl
                ));
            }
            
        } catch (Exception e) {
            source.sendFailure(Component.literal("§c播放音乐失败: " + e.getMessage()));
            return 0;
        }
        
        return targets.size();
    }
    
    private static boolean isValidUrl(String urlString) {
        try {
            URL url = new URL(urlString);
            String protocol = url.getProtocol();
            return "http".equals(protocol) || "https".equals(protocol);
        } catch (MalformedURLException e) {
            return false;
        }
    }
    
    private static boolean isSupportedAudioFormat(String urlString) {
        String lower = urlString.toLowerCase();
        return lower.endsWith(".mp3") ||
               lower.endsWith(".wav") ||
               lower.endsWith(".aiff") ||
               lower.endsWith(".au");
    }
}
