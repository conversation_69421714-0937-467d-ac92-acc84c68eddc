package top.lacrus.kyokuerabu.client;

import com.mojang.logging.LogUtils;
import javazoom.jlayer.player.Player;
import net.minecraft.client.Minecraft;
import net.minecraft.client.resources.sounds.SimpleSoundInstance;
import net.minecraft.client.sounds.SoundManager;
import net.minecraft.network.chat.Component;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundSource;
import org.slf4j.Logger;

import javax.sound.sampled.*;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.net.URL;
import java.util.concurrent.CompletableFuture;

public class ClientMusicPlayer {
    private static final Logger LOGGER = LogUtils.getLogger();
    private static Clip currentClip;
    
    public static void playMusic(String musicUrl) {
        try {
            // 停止当前播放的音乐
            stopCurrentMusic();
            
            // 在后台线程中加载和播放音乐
            CompletableFuture.runAsync(() -> {
                try {
                    URL url = new URL(musicUrl);
                    AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(
                        new BufferedInputStream(url.openStream())
                    );
                    
                    currentClip = AudioSystem.getClip();
                    currentClip.open(audioInputStream);
                    
                    // 设置音量（可选）
                    FloatControl volumeControl = (FloatControl) currentClip.getControl(FloatControl.Type.MASTER_GAIN);
                    volumeControl.setValue(-10.0f); // 降低音量
                    
                    currentClip.start();
                    
                    // 向玩家发送消息
                    Minecraft.getInstance().execute(() -> {
                        if (Minecraft.getInstance().player != null) {
                            Minecraft.getInstance().player.sendSystemMessage(
                                Component.literal("§a[Kyokuerabu] 开始播放音乐: " + musicUrl)
                            );
                        }
                    });
                    
                    LOGGER.info("Started playing music: {}", musicUrl);
                    
                } catch (Exception e) {
                    LOGGER.error("Failed to play music: {}", musicUrl, e);
                    
                    // 向玩家发送错误消息
                    Minecraft.getInstance().execute(() -> {
                        if (Minecraft.getInstance().player != null) {
                            Minecraft.getInstance().player.sendSystemMessage(
                                Component.literal("§c[Kyokuerabu] 播放音乐失败: " + e.getMessage())
                            );
                        }
                    });
                }
            });
            
        } catch (Exception e) {
            LOGGER.error("Error starting music playback: {}", musicUrl, e);
        }
    }
    
    public static void stopCurrentMusic() {
        if (currentClip != null && currentClip.isRunning()) {
            currentClip.stop();
            currentClip.close();
            currentClip = null;
            
            LOGGER.info("Stopped current music");
            
            // 向玩家发送消息
            if (Minecraft.getInstance().player != null) {
                Minecraft.getInstance().player.sendSystemMessage(
                    Component.literal("§e[Kyokuerabu] 停止播放音乐")
                );
            }
        }
    }
    
    public static boolean isPlaying() {
        return currentClip != null && currentClip.isRunning();
    }
}
