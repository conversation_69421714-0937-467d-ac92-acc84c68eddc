package top.lacrus.kyokuerabu.client;

import com.mojang.logging.LogUtils;
import net.minecraft.client.Minecraft;
import net.minecraft.network.chat.Component;
import org.slf4j.Logger;

import javax.sound.sampled.*;
import java.io.*;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.concurrent.CompletableFuture;

public class ClientMusicPlayer {
    private static final Logger LOGGER = LogUtils.getLogger();
    private static Player currentMp3Player;
    private static Thread currentMp3Thread;
    private static Clip currentClip;
    private static volatile boolean shouldStop = false;
    private static Path tempMusicFile = null;
    public static void playMusic(String musicUrl) {
        System.out.println("[Kyokuerabu] ClientMusicPlayer.playMusic called with URL: " + musicUrl);
        LOGGER.info("ClientMusicPlayer.playMusic called with URL: {}", musicUrl);

        // 向玩家发送开始消息
        if (Minecraft.getInstance().player != null) {
            Minecraft.getInstance().player.sendSystemMessage(
                Component.literal("§a[Kyokuerabu] 开始下载音乐: " + musicUrl)
            );
        }

        // 停止当前播放的音乐
        stopCurrentMusic();

        // 在后台线程中下载并播放音乐
        CompletableFuture.runAsync(() -> {
            try {
                // 下载音乐到临时文件
                Path downloadedFile = downloadMusicToTemp(musicUrl);
                if (downloadedFile != null) {
                    tempMusicFile = downloadedFile;

                    // 根据文件类型播放音乐
                    String urlLower = musicUrl.toLowerCase();
                    if (urlLower.endsWith(".mp3")) {
                        playMp3FromFile(downloadedFile, musicUrl);
                    } else {
                        playStandardFromFile(downloadedFile, musicUrl);
                    }
                }

            } catch (Exception e) {
                System.out.println("[Kyokuerabu] Exception in playMusic: " + e.getClass().getSimpleName() + ": " + e.getMessage());
                e.printStackTrace();
                LOGGER.error("Failed to play music: {}", musicUrl, e);

                // 向玩家发送错误消息
                Minecraft.getInstance().execute(() -> {
                    if (Minecraft.getInstance().player != null) {
                        Minecraft.getInstance().player.sendSystemMessage(
                            Component.literal("§c[Kyokuerabu] 播放音乐失败: " + e.getClass().getSimpleName() + ": " + e.getMessage())
                        );
                    }
                });
            }
        });
    }

    private static Path downloadMusicToTemp(String musicUrl) {
        try {
            System.out.println("[Kyokuerabu] Starting download from: " + musicUrl);

            // 向玩家发送下载开始消息
            Minecraft.getInstance().execute(() -> {
                if (Minecraft.getInstance().player != null) {
                    Minecraft.getInstance().player.sendSystemMessage(
                        Component.literal("§e[Kyokuerabu] 正在下载音乐文件...")
                    );
                }
            });

            URL url = new URL(musicUrl);
            String fileName = "kyokuerabu_music_" + System.currentTimeMillis();

            // 根据URL确定文件扩展名
            if (musicUrl.toLowerCase().endsWith(".mp3")) {
                fileName += ".mp3";
            } else if (musicUrl.toLowerCase().endsWith(".wav")) {
                fileName += ".wav";
            } else if (musicUrl.toLowerCase().endsWith(".aiff")) {
                fileName += ".aiff";
            } else if (musicUrl.toLowerCase().endsWith(".au")) {
                fileName += ".au";
            } else {
                fileName += ".tmp";
            }

            // 创建临时文件
            Path tempFile = Files.createTempFile("kyokuerabu_", fileName);

            // 下载文件
            try (InputStream in = new BufferedInputStream(url.openStream())) {
                Files.copy(in, tempFile, StandardCopyOption.REPLACE_EXISTING);
            }

            System.out.println("[Kyokuerabu] Downloaded to: " + tempFile.toString());

            // 向玩家发送下载完成消息
            Minecraft.getInstance().execute(() -> {
                if (Minecraft.getInstance().player != null) {
                    Minecraft.getInstance().player.sendSystemMessage(
                        Component.literal("§a[Kyokuerabu] 音乐下载完成，开始播放...")
                    );
                }
            });

            return tempFile;

        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Download failed: " + e.getMessage());
            e.printStackTrace();

            // 向玩家发送下载失败消息
            Minecraft.getInstance().execute(() -> {
                if (Minecraft.getInstance().player != null) {
                    Minecraft.getInstance().player.sendSystemMessage(
                        Component.literal("§c[Kyokuerabu] 音乐下载失败: " + e.getMessage())
                    );
                }
            });

            return null;
        }
    }

    private static void playMp3FromFile(Path filePath, String musicUrl) {
        try {
            System.out.println("[Kyokuerabu] Starting MP3 playback from file: " + filePath);

            FileInputStream fis = new FileInputStream(filePath.toFile());
            BufferedInputStream bis = new BufferedInputStream(fis);
            currentMp3Player = new Player(bis);
            shouldStop = false;

            System.out.println("[Kyokuerabu] MP3 Player initialized successfully");

            // 在新线程中播放MP3
            currentMp3Thread = new Thread(() -> {
                try {
                    // 向玩家发送开始播放消息
                    Minecraft.getInstance().execute(() -> {
                        if (Minecraft.getInstance().player != null) {
                            Minecraft.getInstance().player.sendSystemMessage(
                                Component.literal("§a[Kyokuerabu] 开始播放MP3音乐: " + musicUrl)
                            );
                        }
                    });

                    LOGGER.info("Started playing MP3 music: {}", musicUrl);

                    // 播放音乐
                    currentMp3Player.play();

                    // 播放完成
                    if (!shouldStop) {
                        Minecraft.getInstance().execute(() -> {
                            if (Minecraft.getInstance().player != null) {
                                Minecraft.getInstance().player.sendSystemMessage(
                                    Component.literal("§e[Kyokuerabu] 音乐播放完成")
                                );
                            }
                        });
                    }

                } catch (JavaLayerException e) {
                    if (!shouldStop) {
                        LOGGER.error("Error during MP3 playback: {}", musicUrl, e);
                    }
                } finally {
                    cleanup();
                }
            });

            currentMp3Thread.setDaemon(true);
            currentMp3Thread.start();

        } catch (Exception e) {
            LOGGER.error("Failed to initialize MP3 player: {}", musicUrl, e);
            throw new RuntimeException(e);
        }
    }



    private static void playStandardFromFile(Path filePath, String musicUrl) {
        try {
            System.out.println("[Kyokuerabu] Starting standard audio playback from file: " + filePath);

            FileInputStream fis = new FileInputStream(filePath.toFile());
            AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(
                new BufferedInputStream(fis)
            );

            currentClip = AudioSystem.getClip();
            currentClip.open(audioInputStream);

            // 设置音量
            try {
                FloatControl volumeControl = (FloatControl) currentClip.getControl(FloatControl.Type.MASTER_GAIN);
                volumeControl.setValue(-10.0f); // 降低音量
            } catch (Exception e) {
                LOGGER.warn("Could not set volume control: {}", e.getMessage());
            }

            // 向玩家发送开始播放消息
            Minecraft.getInstance().execute(() -> {
                if (Minecraft.getInstance().player != null) {
                    Minecraft.getInstance().player.sendSystemMessage(
                        Component.literal("§a[Kyokuerabu] 开始播放音乐: " + musicUrl)
                    );
                }
            });

            LOGGER.info("Started playing standard audio: {}", musicUrl);

            currentClip.start();

            // 等待播放完成
            while (currentClip.isRunning() && !shouldStop) {
                Thread.sleep(100);
            }

            // 播放完成
            if (!shouldStop) {
                Minecraft.getInstance().execute(() -> {
                    if (Minecraft.getInstance().player != null) {
                        Minecraft.getInstance().player.sendSystemMessage(
                            Component.literal("§e[Kyokuerabu] 音乐播放完成")
                        );
                    }
                });
            }

        } catch (Exception e) {
            LOGGER.error("Failed to play standard audio: {}", musicUrl, e);
            throw new RuntimeException(e);
        } finally {
            cleanup();
        }
    }

    public static void stopCurrentMusic() {
        shouldStop = true;

        // 停止MP3播放
        if (currentMp3Player != null) {
            currentMp3Player.close();
        }

        if (currentMp3Thread != null && currentMp3Thread.isAlive()) {
            currentMp3Thread.interrupt();
        }

        // 停止标准音频播放
        if (currentClip != null && currentClip.isRunning()) {
            currentClip.stop();
            currentClip.close();
        }

        cleanup();

        LOGGER.info("Stopped current music");

        // 向玩家发送消息
        if (Minecraft.getInstance().player != null) {
            Minecraft.getInstance().player.sendSystemMessage(
                Component.literal("§e[Kyokuerabu] 停止播放音乐")
            );
        }
    }

    private static void cleanup() {
        currentMp3Player = null;
        currentMp3Thread = null;
        currentClip = null;

        // 删除临时音乐文件
        if (tempMusicFile != null) {
            try {
                Files.deleteIfExists(tempMusicFile);
                System.out.println("[Kyokuerabu] Deleted temporary music file: " + tempMusicFile);
            } catch (Exception e) {
                System.out.println("[Kyokuerabu] Failed to delete temporary file: " + e.getMessage());
            }
            tempMusicFile = null;
        }
    }

    // 添加一个静态块来在JVM关闭时清理临时文件
    static {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            if (tempMusicFile != null) {
                try {
                    Files.deleteIfExists(tempMusicFile);
                    System.out.println("[Kyokuerabu] Cleanup: Deleted temporary music file on shutdown");
                } catch (Exception e) {
                    System.out.println("[Kyokuerabu] Cleanup: Failed to delete temporary file on shutdown: " + e.getMessage());
                }
            }
        }));
    }

    public static boolean isPlaying() {
        return (currentMp3Player != null) ||
               (currentMp3Thread != null && currentMp3Thread.isAlive()) ||
               (currentClip != null && currentClip.isRunning());
    }
}
