package top.lacrus.kyokuerabu.client;

import com.mojang.logging.LogUtils;
import javazoom.jlayer.player.Player;
import net.minecraft.client.Minecraft;
import net.minecraft.client.resources.sounds.SimpleSoundInstance;
import net.minecraft.client.sounds.SoundManager;
import net.minecraft.network.chat.Component;
import net.minecraft.sounds.SoundEvent;
import net.minecraft.sounds.SoundSource;
import org.slf4j.Logger;

import javax.sound.sampled.*;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.net.URL;
import java.util.concurrent.CompletableFuture;

public class ClientMusicPlayer {
    private static final Logger LOGGER = LogUtils.getLogger();
    private static Clip currentClip;
    private static Player currentMp3Player;
    private static Thread currentMp3Thread;

    public static void playMusic(String musicUrl) {
        try {
            // 停止当前播放的音乐
            stopCurrentMusic();

            // 在后台线程中加载和播放音乐
            CompletableFuture.runAsync(() -> {
                try {
                    URL url = new URL(musicUrl);
                    String urlLower = musicUrl.toLowerCase();

                    if (urlLower.endsWith(".mp3")) {
                        // 使用JLayer播放MP3
                        playMp3(url, musicUrl);
                    } else {
                        // 使用标准AudioSystem播放其他格式
                        playStandardAudio(url, musicUrl);
                    }

                } catch (Exception e) {
                    LOGGER.error("Failed to play music: {}", musicUrl, e);

                    // 向玩家发送错误消息
                    Minecraft.getInstance().execute(() -> {
                        if (Minecraft.getInstance().player != null) {
                            Minecraft.getInstance().player.sendSystemMessage(
                                Component.literal("§c[Kyokuerabu] 播放音乐失败: " + e.getMessage())
                            );
                        }
                    });
                }
            });

        } catch (Exception e) {
            LOGGER.error("Error starting music playback: {}", musicUrl, e);
        }
    }

    private static void playMp3(URL url, String musicUrl) throws Exception {
        BufferedInputStream bis = new BufferedInputStream(url.openStream());
        currentMp3Player = new Player(bis);

        // 在新线程中播放MP3
        currentMp3Thread = new Thread(() -> {
            try {
                currentMp3Player.play();

                // 播放完成后清理
                Minecraft.getInstance().execute(() -> {
                    if (Minecraft.getInstance().player != null) {
                        Minecraft.getInstance().player.sendSystemMessage(
                            Component.literal("§e[Kyokuerabu] 音乐播放完成")
                        );
                    }
                });

            } catch (Exception e) {
                LOGGER.error("Error during MP3 playback: {}", musicUrl, e);
            } finally {
                currentMp3Player = null;
                currentMp3Thread = null;
            }
        });

        currentMp3Thread.start();

        // 向玩家发送消息
        Minecraft.getInstance().execute(() -> {
            if (Minecraft.getInstance().player != null) {
                Minecraft.getInstance().player.sendSystemMessage(
                    Component.literal("§a[Kyokuerabu] 开始播放MP3音乐: " + musicUrl)
                );
            }
        });

        LOGGER.info("Started playing MP3 music: {}", musicUrl);
    }

    private static void playStandardAudio(URL url, String musicUrl) throws Exception {
        AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(
            new BufferedInputStream(url.openStream())
        );

        currentClip = AudioSystem.getClip();
        currentClip.open(audioInputStream);

        // 设置音量（可选）
        try {
            FloatControl volumeControl = (FloatControl) currentClip.getControl(FloatControl.Type.MASTER_GAIN);
            volumeControl.setValue(-10.0f); // 降低音量
        } catch (Exception e) {
            LOGGER.warn("Could not set volume control: {}", e.getMessage());
        }

        currentClip.start();

        // 向玩家发送消息
        Minecraft.getInstance().execute(() -> {
            if (Minecraft.getInstance().player != null) {
                Minecraft.getInstance().player.sendSystemMessage(
                    Component.literal("§a[Kyokuerabu] 开始播放音乐: " + musicUrl)
                );
            }
        });

        LOGGER.info("Started playing standard audio: {}", musicUrl);
    }
    
    public static void stopCurrentMusic() {
        boolean wasStopped = false;

        // 停止标准音频播放
        if (currentClip != null && currentClip.isRunning()) {
            currentClip.stop();
            currentClip.close();
            currentClip = null;
            wasStopped = true;
        }

        // 停止MP3播放
        if (currentMp3Player != null) {
            currentMp3Player.close();
            currentMp3Player = null;
            wasStopped = true;
        }

        if (currentMp3Thread != null && currentMp3Thread.isAlive()) {
            currentMp3Thread.interrupt();
            currentMp3Thread = null;
            wasStopped = true;
        }

        if (wasStopped) {
            LOGGER.info("Stopped current music");

            // 向玩家发送消息
            if (Minecraft.getInstance().player != null) {
                Minecraft.getInstance().player.sendSystemMessage(
                    Component.literal("§e[Kyokuerabu] 停止播放音乐")
                );
            }
        }
    }

    public static boolean isPlaying() {
        return (currentClip != null && currentClip.isRunning()) ||
               (currentMp3Player != null) ||
               (currentMp3Thread != null && currentMp3Thread.isAlive());
    }
}
