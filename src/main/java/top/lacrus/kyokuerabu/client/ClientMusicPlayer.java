package top.lacrus.kyokuerabu.client;

import com.mojang.logging.LogUtils;
import javazoom.jl.decoder.JavaLayerException;
import javazoom.jl.player.Player;
import net.minecraft.client.Minecraft;
import net.minecraft.network.chat.Component;
import org.slf4j.Logger;

import javax.sound.sampled.*;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.net.URL;
import java.util.concurrent.CompletableFuture;

public class ClientMusicPlayer {
    private static final Logger LOGGER = LogUtils.getLogger();
    private static Player currentMp3Player;
    private static Thread currentMp3Thread;
    private static Clip currentClip;
    private static volatile boolean shouldStop = false;
    public static void playMusic(String musicUrl) {
        // 停止当前播放的音乐
        stopCurrentMusic();

        // 在后台线程中播放音乐
        CompletableFuture.runAsync(() -> {
            try {
                URL url = new URL(musicUrl);
                String urlLower = musicUrl.toLowerCase();

                if (urlLower.endsWith(".mp3")) {
                    playMp3Music(url, musicUrl);
                } else {
                    playStandardMusic(url, musicUrl);
                }

            } catch (Exception e) {
                LOGGER.error("Failed to play music: {}", musicUrl, e);

                // 向玩家发送错误消息
                Minecraft.getInstance().execute(() -> {
                    if (Minecraft.getInstance().player != null) {
                        Minecraft.getInstance().player.sendSystemMessage(
                            Component.literal("§c[Kyokuerabu] 播放音乐失败: " + e.getMessage())
                        );
                    }
                });
            }
        });
    }

    private static void playMp3Music(URL url, String musicUrl) {
        try {
            BufferedInputStream bis = new BufferedInputStream(url.openStream());
            currentMp3Player = new Player(bis);
            shouldStop = false;

            // 在新线程中播放MP3
            currentMp3Thread = new Thread(() -> {
                try {
                    // 向玩家发送开始播放消息
                    Minecraft.getInstance().execute(() -> {
                        if (Minecraft.getInstance().player != null) {
                            Minecraft.getInstance().player.sendSystemMessage(
                                Component.literal("§a[Kyokuerabu] 开始播放MP3音乐: " + musicUrl)
                            );
                        }
                    });

                    LOGGER.info("Started playing MP3 music: {}", musicUrl);

                    // 播放音乐
                    currentMp3Player.play();

                    // 播放完成
                    if (!shouldStop) {
                        Minecraft.getInstance().execute(() -> {
                            if (Minecraft.getInstance().player != null) {
                                Minecraft.getInstance().player.sendSystemMessage(
                                    Component.literal("§e[Kyokuerabu] 音乐播放完成")
                                );
                            }
                        });
                    }

                } catch (JavaLayerException e) {
                    if (!shouldStop) {
                        LOGGER.error("Error during MP3 playback: {}", musicUrl, e);
                    }
                } finally {
                    cleanup();
                }
            });

            currentMp3Thread.setDaemon(true);
            currentMp3Thread.start();

        } catch (Exception e) {
            LOGGER.error("Failed to initialize MP3 player: {}", musicUrl, e);
            throw new RuntimeException(e);
        }
    }



    private static void playStandardMusic(URL url, String musicUrl) {
        try {
            AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(
                new BufferedInputStream(url.openStream())
            );

            currentClip = AudioSystem.getClip();
            currentClip.open(audioInputStream);

            // 设置音量
            try {
                FloatControl volumeControl = (FloatControl) currentClip.getControl(FloatControl.Type.MASTER_GAIN);
                volumeControl.setValue(-10.0f); // 降低音量
            } catch (Exception e) {
                LOGGER.warn("Could not set volume control: {}", e.getMessage());
            }

            // 向玩家发送开始播放消息
            Minecraft.getInstance().execute(() -> {
                if (Minecraft.getInstance().player != null) {
                    Minecraft.getInstance().player.sendSystemMessage(
                        Component.literal("§a[Kyokuerabu] 开始播放音乐: " + musicUrl)
                    );
                }
            });

            LOGGER.info("Started playing standard audio: {}", musicUrl);

            currentClip.start();

            // 等待播放完成
            while (currentClip.isRunning() && !shouldStop) {
                Thread.sleep(100);
            }

            // 播放完成
            if (!shouldStop) {
                Minecraft.getInstance().execute(() -> {
                    if (Minecraft.getInstance().player != null) {
                        Minecraft.getInstance().player.sendSystemMessage(
                            Component.literal("§e[Kyokuerabu] 音乐播放完成")
                        );
                    }
                });
            }

        } catch (Exception e) {
            LOGGER.error("Failed to play standard audio: {}", musicUrl, e);
            throw new RuntimeException(e);
        } finally {
            cleanup();
        }
    }

    public static void stopCurrentMusic() {
        shouldStop = true;

        // 停止MP3播放
        if (currentMp3Player != null) {
            currentMp3Player.close();
        }

        if (currentMp3Thread != null && currentMp3Thread.isAlive()) {
            currentMp3Thread.interrupt();
        }

        // 停止标准音频播放
        if (currentClip != null && currentClip.isRunning()) {
            currentClip.stop();
            currentClip.close();
        }

        cleanup();

        LOGGER.info("Stopped current music");

        // 向玩家发送消息
        if (Minecraft.getInstance().player != null) {
            Minecraft.getInstance().player.sendSystemMessage(
                Component.literal("§e[Kyokuerabu] 停止播放音乐")
            );
        }
    }

    private static void cleanup() {
        currentMp3Player = null;
        currentMp3Thread = null;
        currentClip = null;
    }

    public static boolean isPlaying() {
        return (currentMp3Player != null) ||
               (currentMp3Thread != null && currentMp3Thread.isAlive()) ||
               (currentClip != null && currentClip.isRunning());
    }
}
