package top.lacrus.kyokuerabu.client;

import com.mojang.logging.LogUtils;
import net.minecraft.client.resources.sounds.SoundInstance;
import net.minecraft.client.sounds.AudioStream;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.sounds.SoundSource;
import net.minecraft.util.RandomSource;
import org.slf4j.Logger;
import top.lacrus.kyokuerabu.Kyokuerabu;

import javax.sound.sampled.AudioFormat;
import javax.sound.sampled.AudioInputStream;
import javax.sound.sampled.AudioSystem;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.net.URL;
import java.nio.ByteBuffer;

public class OnlineAudioStream implements SoundInstance {
    private static final Logger LOGGER = LogUtils.getLogger();
    private static final ResourceLocation ONLINE_MUSIC_LOCATION = 
        new ResourceLocation(Kyokuerabu.MODID, "online_music");
    
    private final String musicUrl;
    private AudioInputStream audioInputStream;
    private AudioFormat audioFormat;
    private boolean stopped = false;
    
    public OnlineAudioStream(String musicUrl) {
        this.musicUrl = musicUrl;
        try {
            initializeAudioStream();
        } catch (Exception e) {
            LOGGER.error("Failed to initialize audio stream for: {}", musicUrl, e);
        }
    }
    
    private void initializeAudioStream() throws Exception {
        URL url = new URL(musicUrl);
        BufferedInputStream bis = new BufferedInputStream(url.openStream());
        
        // 尝试获取音频输入流
        try {
            audioInputStream = AudioSystem.getAudioInputStream(bis);
            audioFormat = audioInputStream.getFormat();
            LOGGER.info("Initialized audio stream for: {} with format: {}", musicUrl, audioFormat);
        } catch (Exception e) {
            // 如果是MP3或其他不支持的格式，创建一个默认格式
            audioFormat = new AudioFormat(44100, 16, 2, true, false);
            LOGGER.warn("Could not get audio format for: {}, using default format", musicUrl);
            throw e;
        }
    }
    
    @Override
    public ResourceLocation getLocation() {
        return ONLINE_MUSIC_LOCATION;
    }
    
    @Override
    public SoundSource getSource() {
        return SoundSource.MUSIC;
    }
    
    @Override
    public boolean isLooping() {
        return false;
    }
    
    @Override
    public boolean isRelative() {
        return false;
    }
    
    @Override
    public int getDelay() {
        return 0;
    }
    
    @Override
    public float getVolume() {
        return 1.0f;
    }
    
    @Override
    public float getPitch() {
        return 1.0f;
    }
    
    @Override
    public double getX() {
        return 0;
    }
    
    @Override
    public double getY() {
        return 0;
    }
    
    @Override
    public double getZ() {
        return 0;
    }
    
    @Override
    public Attenuation getAttenuation() {
        return Attenuation.NONE;
    }
    
    public AudioStream createAudioStream() {
        return new OnlineAudioStreamImpl();
    }
    
    public void stop() {
        stopped = true;
        if (audioInputStream != null) {
            try {
                audioInputStream.close();
            } catch (IOException e) {
                LOGGER.error("Error closing audio stream", e);
            }
        }
    }
    
    private class OnlineAudioStreamImpl implements AudioStream {
        @Override
        public AudioFormat getFormat() {
            return audioFormat;
        }
        
        @Override
        public ByteBuffer read(int bufferSize) throws IOException {
            if (stopped || audioInputStream == null) {
                return null;
            }
            
            byte[] buffer = new byte[bufferSize];
            int bytesRead = audioInputStream.read(buffer);
            
            if (bytesRead == -1) {
                return null; // End of stream
            }
            
            return ByteBuffer.wrap(buffer, 0, bytesRead);
        }
        
        @Override
        public void close() throws IOException {
            if (audioInputStream != null) {
                audioInputStream.close();
            }
        }
    }
}
