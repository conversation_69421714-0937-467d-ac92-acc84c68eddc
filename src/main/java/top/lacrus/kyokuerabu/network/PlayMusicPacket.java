package top.lacrus.kyokuerabu.network;

import net.minecraft.network.FriendlyByteBuf;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.fml.DistExecutor;
import net.minecraftforge.network.NetworkEvent;
import top.lacrus.kyokuerabu.client.ClientMusicPlayer;

import java.util.function.Supplier;

public class PlayMusicPacket {
    private final String musicUrl;
    
    public PlayMusicPacket(String musicUrl) {
        this.musicUrl = musicUrl;
    }
    
    public static void encode(PlayMusicPacket packet, FriendlyByteBuf buffer) {
        buffer.writeUtf(packet.musicUrl);
    }
    
    public static PlayMusicPacket decode(FriendlyByteBuf buffer) {
        return new PlayMusicPacket(buffer.readUtf());
    }
    
    public static void handle(PlayMusicPacket packet, Supplier<NetworkEvent.Context> contextSupplier) {
        NetworkEvent.Context context = contextSupplier.get();
        context.enqueueWork(() -> {
            System.out.println("[Kyokuerabu] Received PlayMusicPacket on side: " + context.getDirection());
            System.out.println("[Kyokuerabu] Music URL: " + packet.musicUrl);

            // 直接检查是否在客户端
            if (context.getDirection().getReceptionSide().isClient()) {
                System.out.println("[Kyokuerabu] Processing on client side");
                handleClientSide(packet.musicUrl);
            }
        });
        context.setPacketHandled(true);
    }

    private static void handleClientSide(String musicUrl) {
        try {
            // 使用反射调用客户端方法，避免在服务端加载客户端类
            Class<?> clientPlayerClass = Class.forName("top.lacrus.kyokuerabu.client.ClientMusicPlayer");
            java.lang.reflect.Method playMethod = clientPlayerClass.getMethod("playMusic", String.class);
            playMethod.invoke(null, musicUrl);
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Error calling client music player: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public String getMusicUrl() {
        return musicUrl;
    }
}
