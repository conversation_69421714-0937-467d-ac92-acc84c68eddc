package top.lacrus.kyokuerabu.network;

import net.minecraft.network.FriendlyByteBuf;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.fml.DistExecutor;
import net.minecraftforge.network.NetworkEvent;
import top.lacrus.kyokuerabu.client.ClientMusicPlayer;

import java.util.function.Supplier;

public class PlayMusicPacket {
    private final String musicUrl;
    
    public PlayMusicPacket(String musicUrl) {
        this.musicUrl = musicUrl;
    }
    
    public static void encode(PlayMusicPacket packet, FriendlyByteBuf buffer) {
        buffer.writeUtf(packet.musicUrl);
    }
    
    public static PlayMusicPacket decode(FriendlyByteBuf buffer) {
        return new PlayMusicPacket(buffer.readUtf());
    }
    
    public static void handle(PlayMusicPacket packet, Supplier<NetworkEvent.Context> contextSupplier) {
        NetworkEvent.Context context = contextSupplier.get();
        context.enqueueWork(() -> {
            System.out.println("[Kyo<PERSON>erabu] Received PlayMusicPacket on side: " + context.getDirection());
            System.out.println("[Kyokuerabu] Music URL: " + packet.musicUrl);

            // 确保只在客户端执行
            DistExecutor.unsafeRunWhenOn(Dist.CLIENT, () -> () -> {
                System.out.println("[Kyokuerabu] Executing on client side");
                ClientMusicPlayer.playMusic(packet.musicUrl);
            });
        });
        context.setPacketHandled(true);
    }
    
    public String getMusicUrl() {
        return musicUrl;
    }
}
