package top.lacrus.kyokuerabu.network;

import net.minecraft.resources.ResourceLocation;
import net.minecraftforge.network.NetworkRegistry;
import net.minecraftforge.network.simple.SimpleChannel;
import top.lacrus.kyokuerabu.Kyokuerabu;

public class NetworkHandler {
    private static final String PROTOCOL_VERSION = "1";
    
    public static final SimpleChannel INSTANCE = NetworkRegistry.newSimpleChannel(
        new ResourceLocation(Kyokuerabu.MODID, "main"),
        () -> PROTOCOL_VERSION,
        PROTOCOL_VERSION::equals,
        PROTOCOL_VERSION::equals
    );
    
    private static int packetId = 0;
    
    public static void registerPackets() {
        System.out.println("[Kyokuerabu] Registering network packets...");
        INSTANCE.registerMessage(
            packetId++,
            PlayMusicPacket.class,
            PlayMusicPacket::encode,
            PlayMusicPacket::decode,
            PlayMusicPacket::handle
        );
        System.out.println("[<PERSON><PERSON><PERSON><PERSON><PERSON>] PlayMusicPacket registered with ID: " + (packetId - 1));
    }
}
