package top.lacrus.kyokuerabu.audio;

import com.mojang.logging.LogUtils;
import javazoom.jl.decoder.JavaLayerException;
import javazoom.jl.player.Player;
import org.slf4j.Logger;

import javax.sound.sampled.*;
import java.io.BufferedInputStream;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.concurrent.CompletableFuture;

public class SimpleAudioPlayer {
    private static final Logger LOGGER = LogUtils.getLogger();
    private static Player currentMp3Player;
    private static Thread currentMp3Thread;
    private static Clip currentClip;
    private static volatile boolean shouldStop = false;
    private static Path tempMusicFile = null;
    
    public static void playMusic(String musicUrl) {
        System.out.println("[Kyokuerabu] SimpleAudioPlayer.playMusic called with URL: " + musicUrl);
        LOGGER.info("SimpleAudioPlayer.playMusic called with URL: {}", musicUrl);
        
        // 停止当前播放的音乐
        stopCurrentMusic();
        
        // 在后台线程中下载并播放音乐
        CompletableFuture.runAsync(() -> {
            try {
                System.out.println("[Kyokuerabu] Starting download and playback process");
                
                // 下载音乐到临时文件
                Path downloadedFile = downloadMusicToTemp(musicUrl);
                if (downloadedFile != null) {
                    tempMusicFile = downloadedFile;
                    
                    // 根据文件类型播放音乐
                    String urlLower = musicUrl.toLowerCase();
                    if (urlLower.endsWith(".mp3")) {
                        playMp3FromFile(downloadedFile, musicUrl);
                    } else {
                        playStandardFromFile(downloadedFile, musicUrl);
                    }
                }
                
            } catch (Exception e) {
                System.out.println("[Kyokuerabu] Exception in playMusic: " + e.getClass().getSimpleName() + ": " + e.getMessage());
                e.printStackTrace();
                LOGGER.error("Failed to play music: {}", musicUrl, e);
            }
        });
    }
    
    private static Path downloadMusicToTemp(String musicUrl) {
        try {
            System.out.println("[Kyokuerabu] Starting download from: " + musicUrl);
            
            URL url = new URL(musicUrl);
            String fileName = "kyokuerabu_music_" + System.currentTimeMillis();
            
            // 根据URL确定文件扩展名
            if (musicUrl.toLowerCase().endsWith(".mp3")) {
                fileName += ".mp3";
            } else if (musicUrl.toLowerCase().endsWith(".wav")) {
                fileName += ".wav";
            } else if (musicUrl.toLowerCase().endsWith(".aiff")) {
                fileName += ".aiff";
            } else if (musicUrl.toLowerCase().endsWith(".au")) {
                fileName += ".au";
            } else {
                fileName += ".tmp";
            }
            
            // 创建临时文件
            Path tempFile = Files.createTempFile("kyokuerabu_", fileName);
            
            // 下载文件
            try (InputStream in = new BufferedInputStream(url.openStream())) {
                Files.copy(in, tempFile, StandardCopyOption.REPLACE_EXISTING);
            }
            
            System.out.println("[Kyokuerabu] Downloaded to: " + tempFile.toString());
            System.out.println("[Kyokuerabu] File size: " + Files.size(tempFile) + " bytes");
            
            return tempFile;
            
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Download failed: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    private static void playMp3FromFile(Path filePath, String musicUrl) {
        try {
            System.out.println("[Kyokuerabu] Starting MP3 playback from file: " + filePath);
            
            FileInputStream fis = new FileInputStream(filePath.toFile());
            BufferedInputStream bis = new BufferedInputStream(fis);
            currentMp3Player = new Player(bis);
            shouldStop = false;
            
            System.out.println("[Kyokuerabu] MP3 Player initialized successfully");
            
            // 在新线程中播放MP3
            currentMp3Thread = new Thread(() -> {
                try {
                    System.out.println("[Kyokuerabu] Starting MP3 playback thread");
                    
                    // 播放音乐
                    currentMp3Player.play();
                    
                    System.out.println("[Kyokuerabu] MP3 playback completed");
                    
                } catch (JavaLayerException e) {
                    if (!shouldStop) {
                        System.out.println("[Kyokuerabu] Error during MP3 playback: " + e.getMessage());
                        LOGGER.error("Error during MP3 playback: {}", musicUrl, e);
                    }
                } finally {
                    cleanup();
                }
            });
            
            currentMp3Thread.setDaemon(true);
            currentMp3Thread.start();
            
            System.out.println("[Kyokuerabu] MP3 playback thread started");
            
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Failed to initialize MP3 player: " + e.getMessage());
            LOGGER.error("Failed to initialize MP3 player: {}", musicUrl, e);
            throw new RuntimeException(e);
        }
    }
    
    private static void playStandardFromFile(Path filePath, String musicUrl) {
        try {
            System.out.println("[Kyokuerabu] Starting standard audio playback from file: " + filePath);
            
            FileInputStream fis = new FileInputStream(filePath.toFile());
            AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(
                new BufferedInputStream(fis)
            );
            
            currentClip = AudioSystem.getClip();
            currentClip.open(audioInputStream);
            
            // 设置音量
            try {
                FloatControl volumeControl = (FloatControl) currentClip.getControl(FloatControl.Type.MASTER_GAIN);
                volumeControl.setValue(-10.0f); // 降低音量
            } catch (Exception e) {
                System.out.println("[Kyokuerabu] Could not set volume control: " + e.getMessage());
            }
            
            System.out.println("[Kyokuerabu] Starting standard audio playback");
            
            currentClip.start();
            
            // 等待播放完成
            while (currentClip.isRunning() && !shouldStop) {
                Thread.sleep(100);
            }
            
            System.out.println("[Kyokuerabu] Standard audio playback completed");
            
        } catch (Exception e) {
            System.out.println("[Kyokuerabu] Failed to play standard audio: " + e.getMessage());
            LOGGER.error("Failed to play standard audio: {}", musicUrl, e);
            throw new RuntimeException(e);
        } finally {
            cleanup();
        }
    }
    
    public static void stopCurrentMusic() {
        shouldStop = true;
        
        // 停止MP3播放
        if (currentMp3Player != null) {
            currentMp3Player.close();
        }
        
        if (currentMp3Thread != null && currentMp3Thread.isAlive()) {
            currentMp3Thread.interrupt();
        }
        
        // 停止标准音频播放
        if (currentClip != null && currentClip.isRunning()) {
            currentClip.stop();
            currentClip.close();
        }
        
        cleanup();
        
        System.out.println("[Kyokuerabu] Stopped current music");
        LOGGER.info("Stopped current music");
    }
    
    private static void cleanup() {
        currentMp3Player = null;
        currentMp3Thread = null;
        currentClip = null;
        
        // 删除临时音乐文件
        if (tempMusicFile != null) {
            try {
                Files.deleteIfExists(tempMusicFile);
                System.out.println("[Kyokuerabu] Deleted temporary music file: " + tempMusicFile);
            } catch (Exception e) {
                System.out.println("[Kyokuerabu] Failed to delete temporary file: " + e.getMessage());
            }
            tempMusicFile = null;
        }
    }
    
    public static boolean isPlaying() {
        return (currentMp3Player != null) ||
               (currentMp3Thread != null && currentMp3Thread.isAlive()) ||
               (currentClip != null && currentClip.isRunning());
    }
    
    // 添加一个静态块来在JVM关闭时清理临时文件
    static {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            if (tempMusicFile != null) {
                try {
                    Files.deleteIfExists(tempMusicFile);
                    System.out.println("[Kyokuerabu] Cleanup: Deleted temporary music file on shutdown");
                } catch (Exception e) {
                    System.out.println("[Kyokuerabu] Cleanup: Failed to delete temporary file on shutdown: " + e.getMessage());
                }
            }
        }));
    }
}
